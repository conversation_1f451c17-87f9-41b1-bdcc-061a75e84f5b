"""
Web search functionality using SearXNG and content fetching.
"""

import logging
import requests
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import time
import re
from bs4 import BeautifulSoup

# Configure logging
logger = logging.getLogger(__name__)

# Configuration
SEARXNG_BASE_URL = "http://localhost:8888"
SEARCH_TIMEOUT = 30
FETCH_TIMEOUT = 15
MAX_CONTENT_LENGTH = 81920  # Limit content size to prevent memory issues (80KB)
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"


class WebSearchError(Exception):
    """Custom exception for web search related errors."""
    pass


class ContentFetchError(Exception):
    """Custom exception for content fetching related errors."""
    pass


def search_web(query: str, num_results: int = 2) -> List[Dict[str, str]]:
    """
    Search the web using SearXNG and return search results.

    Args:
        query: Search query string
        num_results: Number of results to return (default: 2)

    Returns:
        List of dictionaries containing 'title', 'url', and 'snippet'

    Raises:
        WebSearchError: If search fails
    """
    if not query or not query.strip():
        raise WebSearchError("Search query cannot be empty")

    try:
        logger.info(f"Searching for: '{query}' (requesting {num_results} results)")

        # Prepare search parameters
        search_params = {
            'q': query.strip(),
            'format': 'json',
            'categories': 'general',
            'engines': 'google,bing,duckduckgo',  # Use multiple search engines
            'safesearch': '0',  # No safe search filtering
            'pageno': '1'
        }

        # Make request to SearXNG
        search_url = f"{SEARXNG_BASE_URL}/search"
        headers = {
            'User-Agent': USER_AGENT,
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9'
        }

        response = requests.get(
            search_url,
            params=search_params,
            headers=headers,
            timeout=SEARCH_TIMEOUT
        )

        if response.status_code != 200:
            raise WebSearchError(f"SearXNG returned status code {response.status_code}: {response.text}")

        # Parse JSON response
        try:
            search_data = response.json()
        except ValueError as e:
            raise WebSearchError(f"Invalid JSON response from SearXNG: {str(e)}")

        # Extract results
        results = search_data.get('results', [])
        if not results:
            logger.warning(f"No search results found for query: '{query}'")
            return []

        # Process and limit results
        processed_results = []
        for result in results[:num_results]:
            processed_result = {
                'title': result.get('title', 'No Title'),
                'url': result.get('url', ''),
                'snippet': result.get('content', result.get('description', ''))
            }

            # Validate URL
            if processed_result['url'] and _is_valid_url(processed_result['url']):
                processed_results.append(processed_result)
                logger.info(f"Found result: {processed_result['title'][:50]}... - {processed_result['url']}")
            else:
                logger.warning(f"Skipping result with invalid URL: {processed_result['url']}")

        logger.info(f"Successfully retrieved {len(processed_results)} search results")
        return processed_results

    except requests.exceptions.Timeout:
        raise WebSearchError(f"Search request timed out after {SEARCH_TIMEOUT} seconds. Check if SearXNG is running on {SEARXNG_BASE_URL}")
    except requests.exceptions.ConnectionError:
        raise WebSearchError(f"Cannot connect to SearXNG at {SEARXNG_BASE_URL}. Please ensure SearXNG Docker container is running")
    except requests.exceptions.RequestException as e:
        raise WebSearchError(f"Search request failed: {str(e)}")
    except Exception as e:
        raise WebSearchError(f"Unexpected error during search: {str(e)}")


def fetch_url_content(url: str) -> Tuple[str, str]:
    """
    Fetch and extract text content from a URL.

    Args:
        url: URL to fetch content from

    Returns:
        Tuple of (title, content) strings

    Raises:
        ContentFetchError: If content fetching fails
    """
    if not url or not _is_valid_url(url):
        raise ContentFetchError(f"Invalid URL: {url}")

    try:
        logger.info(f"Fetching content from: {url}")

        headers = {
            'User-Agent': USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        response = requests.get(
            url,
            headers=headers,
            timeout=FETCH_TIMEOUT,
            allow_redirects=True,
            stream=True  # Stream to check content length
        )

        if response.status_code != 200:
            raise ContentFetchError(f"HTTP {response.status_code} error fetching {url}")

        # Get content with size limit - truncate if too large instead of erroring
        content = response.content[:MAX_CONTENT_LENGTH]
        was_truncated = len(response.content) > MAX_CONTENT_LENGTH

        # Parse HTML content
        soup = BeautifulSoup(content, 'html.parser')

        # Extract title
        title_tag = soup.find('title')
        title = title_tag.get_text().strip() if title_tag else "No Title"

        # Remove script and style elements
        for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
            script.decompose()

        # Extract text content
        text_content = soup.get_text()

        # Clean up text
        lines = (line.strip() for line in text_content.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        clean_content = ' '.join(chunk for chunk in chunks if chunk)

        # Limit content length and add truncation notice if needed
        if len(clean_content) > MAX_CONTENT_LENGTH:
            clean_content = clean_content[:MAX_CONTENT_LENGTH] + "... [Content truncated]"
            was_truncated = True

        # Log with truncation info
        truncation_info = " (truncated)" if was_truncated else ""
        logger.info(f"Successfully fetched content from {url}: {len(clean_content)} characters{truncation_info}")
        return title, clean_content

    except requests.exceptions.Timeout:
        raise ContentFetchError(f"Request timed out after {FETCH_TIMEOUT} seconds for {url}")
    except requests.exceptions.ConnectionError:
        raise ContentFetchError(f"Connection error fetching {url}")
    except requests.exceptions.RequestException as e:
        raise ContentFetchError(f"Request failed for {url}: {str(e)}")
    except Exception as e:
        raise ContentFetchError(f"Unexpected error fetching {url}: {str(e)}")


def web_search_and_fetch(query: str, num_results: int = 2) -> Dict:
    """
    Complete web search workflow: search for query and fetch content from top results.

    Args:
        query: Search query string
        num_results: Number of results to search and fetch (default: 2)

    Returns:
        Dictionary containing search results with fetched content
    """
    if not query or not query.strip():
        raise WebSearchError("Search query cannot be empty")

    start_time = time.time()
    logger.info(f"Starting web search and fetch for query: '{query}'")

    try:
        # Step 1: Search for URLs
        search_results = search_web(query, num_results)

        if not search_results:
            return {
                'status': 'success',
                'query': query,
                'message': 'No search results found',
                'results': [],
                'total_results': 0,
                'processing_time': round(time.time() - start_time, 2)
            }

        # Step 2: Fetch content from each URL
        enriched_results = []
        successful_fetches = 0

        for i, result in enumerate(search_results, 1):
            url = result['url']
            logger.info(f"Processing result {i}/{len(search_results)}: {url}")

            try:
                # Fetch content
                fetched_title, content = fetch_url_content(url)

                enriched_result = {
                    'search_title': result['title'],
                    'fetched_title': fetched_title,
                    'url': url,
                    'snippet': result['snippet'],
                    'content': content,
                    'content_length': len(content),
                    'fetch_status': 'success'
                }

                enriched_results.append(enriched_result)
                successful_fetches += 1

                # Small delay between requests to be respectful
                if i < len(search_results):
                    time.sleep(1)

            except ContentFetchError as e:
                logger.warning(f"Failed to fetch content from {url}: {str(e)}")

                # Add result with error info
                enriched_result = {
                    'search_title': result['title'],
                    'fetched_title': None,
                    'url': url,
                    'snippet': result['snippet'],
                    'content': None,
                    'content_length': 0,
                    'fetch_status': 'failed',
                    'fetch_error': str(e)
                }

                enriched_results.append(enriched_result)

        processing_time = round(time.time() - start_time, 2)

        logger.info(f"Web search and fetch completed: {successful_fetches}/{len(search_results)} successful fetches in {processing_time}s")

        return {
            'status': 'success',
            'query': query,
            'message': f'Successfully processed {len(search_results)} search results, {successful_fetches} content fetches successful',
            'results': enriched_results,
            'total_results': len(enriched_results),
            'successful_fetches': successful_fetches,
            'processing_time': processing_time
        }

    except WebSearchError as e:
        logger.error(f"Web search failed: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in web search and fetch: {str(e)}")
        raise WebSearchError(f"Web search and fetch failed: {str(e)}")


def _is_valid_url(url: str) -> bool:
    """
    Validate if a URL is properly formatted and uses http/https.

    Args:
        url: URL string to validate

    Returns:
        True if URL is valid, False otherwise
    """
    try:
        parsed = urlparse(url)
        return bool(parsed.netloc) and parsed.scheme in ('http', 'https')
    except Exception:
        return False